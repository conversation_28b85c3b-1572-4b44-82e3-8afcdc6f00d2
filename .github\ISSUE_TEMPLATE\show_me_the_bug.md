---
name: "🪲 Show me the Bug"  
about: Something happened when I use MetaGPT, I want to report it and hope to get help from the official and community.  
title: ''
labels: kind/bug  
assignees: ''
---

**Bug description**
<!-- Clearly and directly describe the current bug -->

**Bug solved method**
<!-- If you solved the bug, describe the idea or process to solve the current bug. Of course, you can also paste the URL address of your Pull Request. -->
<!-- If not, provide more auxiliary information to facilitate our further positioning and investigation  -->

**Environment information**
<!-- Environment：System version (like ubuntu 22.04), Python version (conda python 3.7), LLM type and model (OpenAI gpt-4-1106-preview) -->

- LLM type and model name:
- System version:
- Python version:
- MetaGPT version or branch:

<!-- Dependent packagess：the packages version cause the bug(like `pydantic 1.10.8`), installation method（like `pip install metagpt` or `pip install from source` or `run in docker`） -->

- packages version:
- installation method: 

**Screenshots or logs**
<!-- Screenshots or logs of the bug can help us understand the problem more quickly -->