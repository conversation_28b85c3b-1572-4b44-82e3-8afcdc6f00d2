# Experience Pool

## Prerequisites
- Ensure the RAG module is installed: https://docs.deepwisdom.ai/main/en/guide/in_depth_guides/rag_module.html
- Set embedding: https://docs.deepwisdom.ai/main/en/guide/in_depth_guides/rag_module.html
- Set `enabled`、`enable_read` and `enable_write` to `true` in the `exp_pool` section of `config2.yaml`

## Example Files

### 1. decorator.py
Showcases the implementation of the `@exp_cache` decorator.

### 2. init_exp_pool.py
Demonstrates the process of initializing the experience pool.

### 3. manager.py
Illustrates CRUD (Create, Read, Update, Delete) operations for managing experiences in the pool.

### 4. scorer.py
Outlines methods for evaluating and scoring experiences within the pool.
